"use server";

import { revalidate<PERSON>ath } from "next/cache";
import { redirect } from "next/navigation";

import { updateUserProfile, upsertUser } from "@/lib/db/users";
import { createClient } from "@/lib/supabase/server";

/**
 * Server action to sync user with database
 * This should be called from client components instead of directly importing upsertUser
 */
export async function syncUserWithDatabase(userId: string) {
  try {
    const supabase = await createClient();

    const {
      data: { user },
      error,
    } = await supabase.auth.getUser();

    if (error || !user || user.id !== userId) {
      throw new Error("User not authenticated or ID mismatch");
    }

    // Sync user with database
    const dbUser = await upsertUser(user);

    return { success: true, user: dbUser };
  } catch (error) {
    console.error("Error syncing user with database:", error);
    return {
      success: false,
      error: error instanceof Error ? error.message : "Unknown error",
    };
  }
}

/**
 * Server action to update user profile
 * This should be called from client components instead of directly importing updateUserProfile
 */
export async function updateUserProfileAction(updates: {
  fullName?: string | null;
}) {
  try {
    const supabase = await createClient();

    const {
      data: { user },
      error,
    } = await supabase.auth.getUser();

    if (error || !user) {
      throw new Error("User not authenticated");
    }

    // Update user profile in database
    const updatedUser = await updateUserProfile(user.id, updates);

    // Revalidate any cached user data
    revalidatePath("/account");

    return { success: true, user: updatedUser };
  } catch (error) {
    console.error("Error updating user profile:", error);
    return {
      success: false,
      error: error instanceof Error ? error.message : "Unknown error",
    };
  }
}

/**
 * Server action for secure login using Supabase authentication
 * Uses Supabase's built-in password hashing and validation
 */
export async function loginAction(formData: FormData) {
  const email = formData.get("email") as string;
  const password = formData.get("password") as string;

  if (!email || !password) {
    return { success: false, error: "Email and password are required" };
  }

  try {
    const supabase = await createClient();

    // Use Supabase's secure signInWithPassword method
    const { data, error } = await supabase.auth.signInWithPassword({
      email,
      password,
    });

    if (error) {
      console.error("Login error:", error);
      return { success: false, error: error.message };
    }

    if (data.user) {
      // Sync user with database
      await upsertUser(data.user);

      // Revalidate cached data
      revalidatePath("/", "layout");

      // Redirect to main app
      redirect("/calories");
    }

    return { success: false, error: "Login failed" };
  } catch (error) {
    console.error("Server login error:", error);
    return {
      success: false,
      error: error instanceof Error ? error.message : "Login failed",
    };
  }
}

/**
 * Server action for secure signup using Supabase authentication
 * Uses Supabase's built-in password hashing and validation
 */
export async function signupAction(formData: FormData) {
  const email = formData.get("email") as string;
  const password = formData.get("password") as string;

  if (!email || !password) {
    return { success: false, error: "Email and password are required" };
  }

  // Basic password validation (Supabase will do more comprehensive validation)
  if (password.length < 6) {
    return {
      success: false,
      error: "Password must be at least 6 characters long",
    };
  }

  try {
    const supabase = await createClient();

    // Use Supabase's secure signUp method
    const { data, error } = await supabase.auth.signUp({
      email,
      password,
      options: {
        emailRedirectTo: `${process.env.NEXT_PUBLIC_URL || "http://localhost:3000"}/calories`,
      },
    });

    if (error) {
      console.error("Signup error:", error);
      return { success: false, error: error.message };
    }

    if (data.user) {
      // Check if user is immediately confirmed (auto-confirm enabled)
      if (data.session) {
        // User is automatically signed in
        await upsertUser(data.user);
        revalidatePath("/", "layout");
        redirect("/calories");
      } else {
        // Email confirmation required
        redirect("/auth/sign-up-success");
      }
    }

    return { success: false, error: "Signup failed" };
  } catch (error) {
    console.error("Server signup error:", error);
    return {
      success: false,
      error: error instanceof Error ? error.message : "Signup failed",
    };
  }
}

/**
 * Server action for secure password reset using Supabase
 */
export async function resetPasswordAction(formData: FormData) {
  const email = formData.get("email") as string;

  if (!email) {
    return { success: false, error: "Email is required" };
  }

  try {
    const supabase = await createClient();

    const { error } = await supabase.auth.resetPasswordForEmail(email, {
      redirectTo: `${process.env.NEXT_PUBLIC_URL || "http://localhost:3000"}/auth/update-password`,
    });

    if (error) {
      console.error("Password reset error:", error);
      return { success: false, error: error.message };
    }

    return { success: true, message: "Password reset email sent" };
  } catch (error) {
    console.error("Server password reset error:", error);
    return {
      success: false,
      error: error instanceof Error ? error.message : "Password reset failed",
    };
  }
}

/**
 * Server action for secure password update using Supabase
 */
export async function updatePasswordAction(formData: FormData) {
  const password = formData.get("password") as string;

  if (!password) {
    return { success: false, error: "Password is required" };
  }

  if (password.length < 6) {
    return {
      success: false,
      error: "Password must be at least 6 characters long",
    };
  }

  try {
    const supabase = await createClient();

    const { error } = await supabase.auth.updateUser({ password });

    if (error) {
      console.error("Password update error:", error);
      return { success: false, error: error.message };
    }

    revalidatePath("/", "layout");
    redirect("/calories");
  } catch (error) {
    console.error("Server password update error:", error);
    return {
      success: false,
      error: error instanceof Error ? error.message : "Password update failed",
    };
  }
}

/**
 * Server action to handle user authentication and database sync
 */
export async function handleAuthCallback() {
  const supabase = await createClient();

  try {
    // Get the current user from Supabase
    const {
      data: { user },
      error,
    } = await supabase.auth.getUser();

    if (error || !user) {
      console.error("Auth error:", error);
      redirect("/auth/login?error=Authentication failed");
    }

    // Sync user with database
    await upsertUser(user);

    // Revalidate any cached user data
    revalidatePath("/", "layout");

    return { success: true, user };
  } catch (error) {
    console.error("Error in auth callback:", error);
    redirect("/auth/login?error=Database sync failed");
  }
}

/**
 * Server action for secure login using Supabase's signInWithPassword
 * Follows Supabase security best practices - passwords are never exposed in client code
 */
export async function signInAction(formData: FormData) {
  const email = formData.get("email") as string;
  const password = formData.get("password") as string;

  // Basic validation
  if (!email || !password) {
    redirect("/auth/login?error=Email and password are required");
  }

  const supabase = await createClient();

  try {
    // Use Supabase's secure signInWithPassword method
    const { data, error } = await supabase.auth.signInWithPassword({
      email,
      password,
    });

    if (error) {
      console.error("Sign in error:", error);
      // Redirect with user-friendly error message
      const errorMessage = encodeURIComponent(getAuthErrorMessage(error));
      redirect(`/auth/login?error=${errorMessage}`);
    }

    if (data.user) {
      // Sync user with database
      await syncUserWithDatabase(data.user.id);

      // Revalidate layout to update auth state
      revalidatePath("/", "layout");

      // Redirect to main app
      redirect("/calories");
    }
  } catch (error) {
    console.error("Unexpected sign in error:", error);
    redirect("/auth/login?error=An unexpected error occurred");
  }
}

/**
 * Server action for secure signup using Supabase's signUp
 * Follows Supabase security best practices - passwords are never exposed in client code
 */
export async function signUpAction(formData: FormData) {
  const email = formData.get("email") as string;
  const password = formData.get("password") as string;
  const confirmPassword = formData.get("confirmPassword") as string;

  // Basic validation
  if (!email || !password || !confirmPassword) {
    redirect("/auth/sign-up?error=All fields are required");
  }

  if (password !== confirmPassword) {
    redirect("/auth/sign-up?error=Passwords do not match");
  }

  if (password.length < 6) {
    redirect("/auth/sign-up?error=Password must be at least 6 characters long");
  }

  const supabase = await createClient();

  try {
    // Use Supabase's secure signUp method
    const { data, error } = await supabase.auth.signUp({
      email,
      password,
      options: {
        emailRedirectTo: `${process.env.NEXT_PUBLIC_URL || "http://localhost:3000"}/calories`,
      },
    });

    if (error) {
      console.error("Sign up error:", error);
      // Redirect with user-friendly error message
      const errorMessage = encodeURIComponent(getAuthErrorMessage(error));
      redirect(`/auth/sign-up?error=${errorMessage}`);
    }

    if (data.user) {
      // Check if user is immediately confirmed (auto-confirm enabled)
      if (data.session) {
        // User is automatically signed in
        await syncUserWithDatabase(data.user.id);
        revalidatePath("/", "layout");
        redirect("/calories");
      } else {
        // Email confirmation required
        redirect("/auth/sign-up-success");
      }
    }
  } catch (error) {
    console.error("Unexpected sign up error:", error);
    redirect("/auth/sign-up?error=An unexpected error occurred");
  }
}

/**
 * Server action to sign out user
 */
export async function signOutUser() {
  const supabase = await createClient();

  try {
    const { error } = await supabase.auth.signOut();

    if (error) {
      throw error;
    }

    revalidatePath("/", "layout");
    redirect("/auth/login");
  } catch (error) {
    console.error("Sign out error:", error);
    redirect("/auth/login?error=Sign out failed");
  }
}

/**
 * Get current authenticated user with database profile
 */
export async function getCurrentUser() {
  const supabase = await createClient();

  try {
    const {
      data: { user },
      error,
    } = await supabase.auth.getUser();

    if (error || !user) {
      return null;
    }

    // Ensure user exists in database
    const dbUser = await upsertUser(user);

    return {
      ...user,
      profile: dbUser,
    };
  } catch (error) {
    console.error("Error getting current user:", error);
    return null;
  }
}
