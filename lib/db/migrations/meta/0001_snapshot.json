{"id": "cb97b8cf-194e-46d4-a6f3-8c0bc3fa32d2", "prevId": "02370ad1-327b-4847-9923-91e2ad6aebdd", "version": "7", "dialect": "postgresql", "tables": {"public.ingredients": {"name": "ingredients", "schema": "", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true, "default": "gen_random_uuid()"}, "user_id": {"name": "user_id", "type": "uuid", "primaryKey": false, "notNull": true}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "sync_status": {"name": "sync_status", "type": "sync_status", "typeSchema": "public", "primaryKey": false, "notNull": true, "default": "'synced'"}, "last_sync_at": {"name": "last_sync_at", "type": "timestamp", "primaryKey": false, "notNull": false}, "version": {"name": "version", "type": "integer", "primaryKey": false, "notNull": true, "default": 1}, "is_deleted": {"name": "is_deleted", "type": "boolean", "primaryKey": false, "notNull": true, "default": false}, "recipe_id": {"name": "recipe_id", "type": "uuid", "primaryKey": false, "notNull": true}, "name": {"name": "name", "type": "text", "primaryKey": false, "notNull": true}, "quantity": {"name": "quantity", "type": "real", "primaryKey": false, "notNull": true}, "unit": {"name": "unit", "type": "text", "primaryKey": false, "notNull": true}, "order": {"name": "order", "type": "integer", "primaryKey": false, "notNull": true, "default": 0}}, "indexes": {}, "foreignKeys": {"ingredients_user_id_users_id_fk": {"name": "ingredients_user_id_users_id_fk", "tableFrom": "ingredients", "tableTo": "users", "columnsFrom": ["user_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}, "ingredients_recipe_id_recipes_id_fk": {"name": "ingredients_recipe_id_recipes_id_fk", "tableFrom": "ingredients", "tableTo": "recipes", "columnsFrom": ["recipe_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.posts_table": {"name": "posts_table", "schema": "", "columns": {"id": {"name": "id", "type": "serial", "primaryKey": true, "notNull": true}, "title": {"name": "title", "type": "text", "primaryKey": false, "notNull": true}, "content": {"name": "content", "type": "text", "primaryKey": false, "notNull": true}, "user_id": {"name": "user_id", "type": "uuid", "primaryKey": false, "notNull": true}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": true}}, "indexes": {}, "foreignKeys": {"posts_table_user_id_users_id_fk": {"name": "posts_table_user_id_users_id_fk", "tableFrom": "posts_table", "tableTo": "users", "columnsFrom": ["user_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.products": {"name": "products", "schema": "", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true, "default": "gen_random_uuid()"}, "user_id": {"name": "user_id", "type": "uuid", "primaryKey": false, "notNull": true}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "sync_status": {"name": "sync_status", "type": "sync_status", "typeSchema": "public", "primaryKey": false, "notNull": true, "default": "'synced'"}, "last_sync_at": {"name": "last_sync_at", "type": "timestamp", "primaryKey": false, "notNull": false}, "version": {"name": "version", "type": "integer", "primaryKey": false, "notNull": true, "default": 1}, "is_deleted": {"name": "is_deleted", "type": "boolean", "primaryKey": false, "notNull": true, "default": false}, "name": {"name": "name", "type": "text", "primaryKey": false, "notNull": true}, "quantity": {"name": "quantity", "type": "real", "primaryKey": false, "notNull": true}, "kcal": {"name": "kcal", "type": "real", "primaryKey": false, "notNull": true}}, "indexes": {}, "foreignKeys": {"products_user_id_users_id_fk": {"name": "products_user_id_users_id_fk", "tableFrom": "products", "tableTo": "users", "columnsFrom": ["user_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.recipes": {"name": "recipes", "schema": "", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true, "default": "gen_random_uuid()"}, "user_id": {"name": "user_id", "type": "uuid", "primaryKey": false, "notNull": true}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "sync_status": {"name": "sync_status", "type": "sync_status", "typeSchema": "public", "primaryKey": false, "notNull": true, "default": "'synced'"}, "last_sync_at": {"name": "last_sync_at", "type": "timestamp", "primaryKey": false, "notNull": false}, "version": {"name": "version", "type": "integer", "primaryKey": false, "notNull": true, "default": 1}, "is_deleted": {"name": "is_deleted", "type": "boolean", "primaryKey": false, "notNull": true, "default": false}, "name": {"name": "name", "type": "text", "primaryKey": false, "notNull": true}, "original_portions": {"name": "original_portions", "type": "integer", "primaryKey": false, "notNull": true}, "description": {"name": "description", "type": "text", "primaryKey": false, "notNull": false}}, "indexes": {}, "foreignKeys": {"recipes_user_id_users_id_fk": {"name": "recipes_user_id_users_id_fk", "tableFrom": "recipes", "tableTo": "users", "columnsFrom": ["user_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.users": {"name": "users", "schema": "", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true}, "email": {"name": "email", "type": "text", "primaryKey": false, "notNull": false}, "full_name": {"name": "full_name", "type": "text", "primaryKey": false, "notNull": false}, "phone": {"name": "phone", "type": "text", "primaryKey": false, "notNull": false}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {"users_email_unique": {"name": "users_email_unique", "nullsNotDistinct": false, "columns": ["email"]}}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}}, "enums": {"public.sync_status": {"name": "sync_status", "schema": "public", "values": ["synced", "pending", "conflict"]}}, "schemas": {}, "sequences": {}, "roles": {}, "policies": {}, "views": {}, "_meta": {"columns": {}, "schemas": {}, "tables": {}}}