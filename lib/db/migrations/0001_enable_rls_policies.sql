-- Enable Row Level Security (RLS) on all tables
-- This ensures that users can only access their own data

-- Enable RLS on users table
ALTER TABLE users ENABLE ROW LEVEL SECURITY;

-- Enable RLS on products table
ALTER TABLE products ENABLE ROW LEVEL SECURITY;

-- Enable RLS on recipes table
ALTER TABLE recipes ENABLE ROW LEVEL SECURITY;

-- Enable RLS on ingredients table
ALTER TABLE ingredients ENABLE ROW LEVEL SECURITY;

-- ============================================================================
-- USERS TABLE POLICIES
-- ============================================================================

-- Users can view their own profile
CREATE POLICY "users_select_own" ON users
  FOR SELECT
  USING (auth.uid() = id);

-- Users can update their own profile
CREATE POLICY "users_update_own" ON users
  FOR UPDATE
  USING (auth.uid() = id);

-- Users can insert their own profile (for initial user creation)
CREATE POLICY "users_insert_own" ON users
  FOR INSERT
  WITH CHECK (auth.uid() = id);

-- ============================================================================
-- PRODUCTS TABLE POLICIES
-- ============================================================================

-- Users can view their own products
CREATE POLICY "products_select_own" ON products
  FOR SELECT
  USING (auth.uid() = user_id);

-- Users can insert their own products
CREATE POLICY "products_insert_own" ON products
  FOR INSERT
  WITH CHECK (auth.uid() = user_id);

-- Users can update their own products
CREATE POLICY "products_update_own" ON products
  FOR UPDATE
  USING (auth.uid() = user_id);

-- Users can delete their own products
CREATE POLICY "products_delete_own" ON products
  FOR DELETE
  USING (auth.uid() = user_id);

-- ============================================================================
-- RECIPES TABLE POLICIES
-- ============================================================================

-- Users can view their own recipes
CREATE POLICY "recipes_select_own" ON recipes
  FOR SELECT
  USING (auth.uid() = user_id);

-- Users can insert their own recipes
CREATE POLICY "recipes_insert_own" ON recipes
  FOR INSERT
  WITH CHECK (auth.uid() = user_id);

-- Users can update their own recipes
CREATE POLICY "recipes_update_own" ON recipes
  FOR UPDATE
  USING (auth.uid() = user_id);

-- Users can delete their own recipes
CREATE POLICY "recipes_delete_own" ON recipes
  FOR DELETE
  USING (auth.uid() = user_id);

-- ============================================================================
-- INGREDIENTS TABLE POLICIES
-- ============================================================================

-- Users can view ingredients from their own recipes
CREATE POLICY "ingredients_select_own" ON ingredients
  FOR SELECT
  USING (auth.uid() = user_id);

-- Users can insert ingredients to their own recipes
CREATE POLICY "ingredients_insert_own" ON ingredients
  FOR INSERT
  WITH CHECK (auth.uid() = user_id);

-- Users can update ingredients in their own recipes
CREATE POLICY "ingredients_update_own" ON ingredients
  FOR UPDATE
  USING (auth.uid() = user_id);

-- Users can delete ingredients from their own recipes
CREATE POLICY "ingredients_delete_own" ON ingredients
  FOR DELETE
  USING (auth.uid() = user_id);

-- ============================================================================
-- ADDITIONAL SECURITY MEASURES
-- ============================================================================

-- Ensure that anonymous users cannot access any data
-- (This is already handled by the auth.uid() checks above, but being explicit)

-- Grant necessary permissions to authenticated users
GRANT SELECT, INSERT, UPDATE, DELETE ON users TO authenticated;
GRANT SELECT, INSERT, UPDATE, DELETE ON products TO authenticated;
GRANT SELECT, INSERT, UPDATE, DELETE ON recipes TO authenticated;
GRANT SELECT, INSERT, UPDATE, DELETE ON ingredients TO authenticated;

-- Revoke all permissions from anonymous users (they should only be able to sign up/in)
REVOKE ALL ON users FROM anon;
REVOKE ALL ON products FROM anon;
REVOKE ALL ON recipes FROM anon;
REVOKE ALL ON ingredients FROM anon;

-- Allow anonymous users to insert into users table (for user registration)
GRANT INSERT ON users TO anon;
