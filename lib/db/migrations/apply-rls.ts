/**
 * <PERSON><PERSON><PERSON> to apply Row Level Security (RLS) policies to the database
 * Run this script to enable <PERSON><PERSON> and create security policies for all tables
 */

import { config } from "dotenv";
import postgres from "postgres";

// Load environment variables
config({ path: ".env.local" });

const sql = postgres(process.env.DATABASE_URL!, { prepare: false });

async function applyRLSPolicies() {
  console.log("🔒 Applying Row Level Security (RLS) policies...");

  try {
    // Enable RLS on all tables
    console.log("📋 Enabling RLS on tables...");
    await sql`ALTER TABLE users ENABLE ROW LEVEL SECURITY`;
    await sql`ALTER TABLE products ENABLE ROW LEVEL SECURITY`;
    await sql`ALTER TABLE recipes ENABLE ROW LEVEL SECURITY`;
    await sql`ALTER TABLE ingredients ENABLE ROW LEVEL SECURITY`;
    console.log("✅ RLS enabled on all tables");

    // Users table policies
    console.log("👤 Creating users table policies...");
    await sql`
      CREATE POLICY "users_select_own" ON users
        FOR SELECT
        USING (auth.uid() = id)
    `;
    await sql`
      CREATE POLICY "users_update_own" ON users
        FOR UPDATE
        USING (auth.uid() = id)
    `;
    await sql`
      CREATE POLICY "users_insert_own" ON users
        FOR INSERT
        WITH CHECK (auth.uid() = id)
    `;
    console.log("✅ Users table policies created");

    // Products table policies
    console.log("🍎 Creating products table policies...");
    await sql`
      CREATE POLICY "products_select_own" ON products
        FOR SELECT
        USING (auth.uid() = user_id)
    `;
    await sql`
      CREATE POLICY "products_insert_own" ON products
        FOR INSERT
        WITH CHECK (auth.uid() = user_id)
    `;
    await sql`
      CREATE POLICY "products_update_own" ON products
        FOR UPDATE
        USING (auth.uid() = user_id)
    `;
    await sql`
      CREATE POLICY "products_delete_own" ON products
        FOR DELETE
        USING (auth.uid() = user_id)
    `;
    console.log("✅ Products table policies created");

    // Recipes table policies
    console.log("📝 Creating recipes table policies...");
    await sql`
      CREATE POLICY "recipes_select_own" ON recipes
        FOR SELECT
        USING (auth.uid() = user_id)
    `;
    await sql`
      CREATE POLICY "recipes_insert_own" ON recipes
        FOR INSERT
        WITH CHECK (auth.uid() = user_id)
    `;
    await sql`
      CREATE POLICY "recipes_update_own" ON recipes
        FOR UPDATE
        USING (auth.uid() = user_id)
    `;
    await sql`
      CREATE POLICY "recipes_delete_own" ON recipes
        FOR DELETE
        USING (auth.uid() = user_id)
    `;
    console.log("✅ Recipes table policies created");

    // Ingredients table policies
    console.log("🥕 Creating ingredients table policies...");
    await sql`
      CREATE POLICY "ingredients_select_own" ON ingredients
        FOR SELECT
        USING (auth.uid() = user_id)
    `;
    await sql`
      CREATE POLICY "ingredients_insert_own" ON ingredients
        FOR INSERT
        WITH CHECK (auth.uid() = user_id)
    `;
    await sql`
      CREATE POLICY "ingredients_update_own" ON ingredients
        FOR UPDATE
        USING (auth.uid() = user_id)
    `;
    await sql`
      CREATE POLICY "ingredients_delete_own" ON ingredients
        FOR DELETE
        USING (auth.uid() = user_id)
    `;
    console.log("✅ Ingredients table policies created");

    // Set up permissions
    console.log("🔐 Setting up permissions...");
    await sql`GRANT SELECT, INSERT, UPDATE, DELETE ON users TO authenticated`;
    await sql`GRANT SELECT, INSERT, UPDATE, DELETE ON products TO authenticated`;
    await sql`GRANT SELECT, INSERT, UPDATE, DELETE ON recipes TO authenticated`;
    await sql`GRANT SELECT, INSERT, UPDATE, DELETE ON ingredients TO authenticated`;

    await sql`REVOKE ALL ON users FROM anon`;
    await sql`REVOKE ALL ON products FROM anon`;
    await sql`REVOKE ALL ON recipes FROM anon`;
    await sql`REVOKE ALL ON ingredients FROM anon`;

    // Allow anonymous users to insert into users table (for registration)
    await sql`GRANT INSERT ON users TO anon`;
    console.log("✅ Permissions configured");

    console.log("🎉 RLS policies applied successfully!");
    console.log("");
    console.log("🔒 Security Summary:");
    console.log("- Users can only access their own data");
    console.log("- All tables are protected by Row Level Security");
    console.log("- Anonymous users can only register new accounts");
    console.log(
      "- Authenticated users have full CRUD access to their own data",
    );
  } catch (error) {
    console.error("❌ Error applying RLS policies:", error);
    throw error;
  } finally {
    await sql.end();
  }
}

// Run the migration if this file is executed directly
if (require.main === module) {
  applyRLSPolicies()
    .then(() => {
      console.log("✅ Migration completed successfully");
      process.exit(0);
    })
    .catch((error) => {
      console.error("❌ Migration failed:", error);
      process.exit(1);
    });
}

export { applyRLSPolicies };
