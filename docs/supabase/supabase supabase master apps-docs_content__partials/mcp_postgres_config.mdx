<Tabs
  scrollable
  size="small"
  type="underlined"
  defaultActiveId="mac"
  queryGroup="os"
>

  <TabPanel id="mac" label="macOS">

    ```json
    {
      "mcpServers": {
        "supabase": {
          "command": "npx",
          "args": ["-y", "@modelcontextprotocol/server-postgres", "<connection-string>"]
        }
      }
    }
    ```

    Replace `<connection-string>` with your connection string.

  </TabPanel>

  <TabPanel id="windows" label="Windows">

    ```json
    {
      "mcpServers": {
        "supabase": {
          "command": "cmd",
          "args": ["/c", "npx", "-y", "@modelcontextprotocol/server-postgres", "<connection-string>"]
        }
      }
    }
    ```

    Replace `<connection-string>` with your connection string.

    <Admonition type="note">

    Make sure that `node` and `npx` are available in your system `PATH`. Assuming `node` is installed, you can get the path by running:

    ```shell
    npm config get prefix
    ```

    Then add it to your system `PATH` by running:

    ```shell
    setx PATH "%PATH%;<path-to-dir>"
    ```

    Replacing `<path-to-dir>` with the path you got from the previous command.

    Finally restart {{ .app }} for the changes to take effect.

    </Admonition>

  </TabPanel>

  <TabPanel id="wsl" label="Windows (WSL)">

    ```json
    {
      "mcpServers": {
        "supabase": {
          "command": "wsl",
          "args": ["npx", "-y", "@modelcontextprotocol/server-postgres", "<connection-string>"]
        }
      }
    }
    ```

    Replace `<connection-string>` with your connection string.

    This assumes you have Windows Subsystem for Linux (WSL) enabled and `node`/`npx` are installed within the WSL environment.

  </TabPanel>

  <TabPanel id="linux" label="Linux">

    ```json
    {
      "mcpServers": {
        "supabase": {
          "command": "npx",
          "args": ["-y", "@modelcontextprotocol/server-postgres", "<connection-string>"]
        }
      }
    }
    ```

    Replace `<connection-string>` with your connection string.

  </TabPanel>

</Tabs>
