---
id: 'auth-general-configuration'
title: 'General configuration'
subtitle: 'General configuration options for Supabase Auth'
---

This section covers the [general configuration options](/dashboard/project/_/settings/auth) for Supabase Auth. If you are looking for another type of configuration, you may be interested in one of the following sections:

- [Provider-specific configuration](/dashboard/project/_/auth/providers)
- [Rate limits](/dashboard/project/_/auth/rate-limits)
- [Email Templates](/dashboard/project/_/auth/templates)
- [Redirect URLs](/dashboard/project/_/auth/url-configuration)
- [Auth Hooks](/dashboard/project/_/auth/hooks)

Supabase Auth provides these [general configuration options](/dashboard/project/_/settings/auth) to control user access to your application:

- **Allow new users to sign up**: Users will be able to sign up. If this config is disabled, only existing users can sign in.

- **Confirm Email**: Users will need to confirm their email address before signing in for the first time.

  - Having **Confirm Email** disabled assumes that the user's email does not need to be verified in order to login and implicitly confirms the user's email in the database.
  - This option can be found in the email provider under the provider-specific configuration.
    {/* - If you previously relied on this config to autoconfirm a user's email address, you can switch to use **Allow unverified email sign in** instead. This new option allows the user to sign in with an unverified email which you can keep track of through the user object. It provides more versatility if you require your users to verify their email address in the future since you can structure your RLS policies to check the user's `email_verified` field. */}

- **Allow anonymous sign-ins**: Allow anonymous users to be created.

- **Allow manual linking**: Allow users to link their accounts manually.
