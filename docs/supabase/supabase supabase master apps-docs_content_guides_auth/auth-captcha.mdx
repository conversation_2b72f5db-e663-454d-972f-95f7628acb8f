---
id: 'auth-captcha'
title: 'Enable CAPTCHA Protection'
description: 'Add CAPTCHA Protection to your Supabase project'
tocVideo: 'em1cpOAXknM'
---

Supabase provides you with the option of adding CAPTCHA to your sign-in, sign-up, and password reset forms. This keeps your website safe from bots and malicious scripts. Supabase authentication has support for [hCaptcha](https://www.hcaptcha.com/) and [Cloudflare Turnstile](https://www.cloudflare.com/products/turnstile/).

## Sign up for CAPTCHA

<Tabs
  scrollable
  size="small"
  type="underlined"
  defaultActiveId="hcaptcha-1"
  queryGroup="captcha-method"
>
<TabPanel id="hcaptcha-1" label="HCaptcha">
Go to the [hCaptcha](https://www.hcaptcha.com/) website and sign up for an account. On the Welcome page, copy the **Sitekey** and **Secret key**.

If you have already signed up and didn't copy this information from the Welcome page, you can get the **Secret key** from the Settings page.

![site_secret_settings.png](/docs/img/guides/auth-captcha/site_secret_settings.png)

The **Sitekey** can be found in the **Settings** of the active site you created.

![sites_dashboard.png](/docs/img/guides/auth-captcha/sites_dashboard.png)

In the Settings page, look for the **Sitekey** section and copy the key.

![sitekey_settings.png](/docs/img/guides/auth-captcha/sitekey_settings.png)

</TabPanel>

<TabPanel id="turnstile-1" label="Turnstile">
 Go to the [Cloudflare website](https://dash.cloudflare.com/login) and sign up for an account. On the Welcome page, head to the Turnstile section and add a new site. Create a site and take note of the **Sitekey** and **Secret Key** as shown below
      ![cloudflare_settings.png](/docs/img/guides/auth-captcha/cloudflare_settings.png)
</TabPanel>
</Tabs>

## Enable CAPTCHA protection for your Supabase project

Navigate to the **[Auth](https://supabase.com/dashboard/project/_/settings/auth)** section of your Project Settings in the Supabase Dashboard and find the **Enable CAPTCHA protection** toggle under Settings > Authentication > Bot and Abuse Protection > Enable CAPTCHA protection.

Select your CAPTCHA provider from the dropdown, enter your CAPTCHA **Secret key**, and click **Save**.

## Add the CAPTCHA frontend component

The frontend requires some changes to provide the CAPTCHA on-screen for the user. This example uses React and the corresponding CAPTCHA React component, but both CAPTCHA providers can be used with any JavaScript framework.

<Tabs
  scrollable
  size="small"
  type="underlined"
  defaultActiveId="hcaptcha-2"
  queryGroup="captcha-method"
>

<TabPanel id="hcaptcha-2" label="HCaptcha">

Install `@hcaptcha/react-hcaptcha` in your project as a dependency.

```bash
npm install @hcaptcha/react-hcaptcha
```

Now import the `HCaptcha` component from the `@hcaptcha/react-hcaptcha` library.

```javascript
import HCaptcha from '@hcaptcha/react-hcaptcha'
```

Let's create a empty state to store our `captchaToken`

```jsx
const [captchaToken, setCaptchaToken] = useState()
```

Now lets add the `HCaptcha` component to the JSX section of our code

```jsx
<HCaptcha />
```

We will pass it the sitekey we copied from the hCaptcha website as a property along with a `onVerify` property which takes a callback function. This callback function will have a token as one of its properties. Let's set the token in the state using `setCaptchaToken`

```jsx
<HCaptcha
  sitekey="your-sitekey"
  onVerify={(token) => {
    setCaptchaToken(token)
  }}
/>
```

Now lets use the CAPTCHA token we receive in our Supabase signUp function.

```jsx
await supabase.auth.signUp({
  email,
  password,
  options: { captchaToken },
})
```

We will also need to reset the CAPTCHA challenge after we have made a call to the function above.

Create a ref to use on our `HCaptcha` component.

```jsx
const captcha = useRef()
```

Let's add a ref attribute on the `HCaptcha` component and assign the `captcha` constant to it.

```jsx
<HCaptcha
  ref={captcha}
  sitekey="your-sitekey"
  onVerify={(token) => {
    setCaptchaToken(token)
  }}
/>
```

Reset the `captcha` after the signUp function is called using the following code:

```jsx
captcha.current.resetCaptcha()
```

In order to test that this works locally we will need to use something like [ngrok](https://ngrok.com/) or add an entry to your hosts file. You can read more about this in the [hCaptcha docs](https://docs.hcaptcha.com/#local-development).

</TabPanel>

<TabPanel id="turnstile-2" label="Turnstile">

The frontend requires some changes to provide the CAPTCHA on-screen for the user. Turnstile can be used with any JavaScript framework but we'll use React and the Turnstile React component for this example.

Install @marsidev/react-turnstile in your project as a dependency.

```bash
npm install @marsidev/react-turnstile
```

Now import the Turnstile component from the @marsidev/react-turnstile library.

```jsx
import { Turnstile } from '@marsidev/react-turnstile'
```

Let's create an empty state to store our `captchaToken`

```jsx
const [captchaToken, setCaptchaToken] = useState()
```

Now lets add the Cloudflare Turnstile component to the JSX section of our code:

```jsx
<Turnstile />
```

We will pass it the sitekey we copied from the Cloudflare website as a property along with a `onSuccess` property which takes a callback function. This callback function will have a token as one of its properties. Let's set the token in the state using `setCaptchaToken`:

```jsx
<Turnstile
  siteKey="your-sitekey"
  onSuccess={(token) => {
    setCaptchaToken(token)
  }}
/>
```

We can now use the `captchaToken` we receive in our Supabase `signUp` function.

```jsx
await supabase.auth.signUp({
  email,
  password,
  options: { captchaToken },
})
```

To test locally, you will need to add localhost to the domain allowlist as per the [Cloudflare docs](https://developers.cloudflare.com/turnstile/reference/testing/)

      </TabPanel>
      </Tabs>

Run the application and you should now be provided with a CAPTCHA challenge.
