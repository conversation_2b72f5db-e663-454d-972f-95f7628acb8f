---
title: 'Signing out'
subtitle: 'Signing out a user'
---

Signing out a user works the same way no matter what method they used to sign in.

Call the sign out method from the client library. It removes the active session and clears Auth data from the storage medium.

<Tabs
  scrollable
  size="small"
  type="underlined"
  defaultActiveId="js"
  queryGroup="language"
>
<TabPanel id="js" label="JavaScript">

```js
import { createClient } from '@supabase/supabase-js'
const supabase = createClient('https://your-project-id.supabase.co', 'your-anon-key')

// ---cut---
async function signOut() {
  const { error } = await supabase.auth.signOut()
}
```

</TabPanel>
<TabPanel id="dart" label="Dart">

```dart
Future<void> signOut() async {
   await supabase.auth.signOut();
}
```

</TabPanel>
<TabPanel id="swift" label="Swift">

```swift
try await supabase.auth.signOut()
```

</TabPanel>
<TabPanel id="kotlin" label="Kotlin">

```kotlin
suspend fun logout() {
	supabase.auth.signOut()
}
```

</TabPanel>
<TabPanel id="python" label="Python">

```python
supabase.auth.sign_out()
```

</TabPanel>
</Tabs>

## Sign out and scopes

Supabase Auth allows you to specify three different scopes for when a user invokes the [sign out API](/docs/reference/javascript/auth-signout) in your application:

- `global` (default) when all sessions active for the user are terminated.
- `local` which only terminates the current session for the user but keep sessions on other devices or browsers active.
- `others` to terminate all but the current session for the user.

You can invoke these by providing the `scope` option:

<Tabs
  scrollable
  size="small"
  type="underlined"
  defaultActiveId="js"
  queryGroup="language"
>
<TabPanel id="js" label="JavaScript">

```js
import { createClient } from '@supabase/supabase-js'
const supabase = createClient('https://your-project-id.supabase.co', 'your-anon-key')

// ---cut---
// defaults to the global scope
await supabase.auth.signOut()

// sign out from the current session only
await supabase.auth.signOut({ scope: 'local' })
```

</TabPanel>
<TabPanel id="dart" label="Dart">

```dart
// defaults to the local scope
await supabase.auth.signOut();

// sign out from all sessions
await supabase.auth.signOut(scope: SignOutScope.global);
```

</TabPanel>
<TabPanel id="kotlin" label="Kotlin">

```kotlin
// defaults to the local scope
await supabase.auth.signOut();

// sign out from all sessions
supabase.auth.signOut(SignOutScope.GLOBAL)
```

</TabPanel>
</Tabs>

Upon sign out, all refresh tokens and potentially other database objects related to the affected sessions are destroyed and the client library removes the session stored in the local storage medium.

<Admonition type="caution">

Access Tokens of revoked sessions remain valid until their expiry time, encoded in the `exp` claim. The user won't be immediately logged out and will only be logged out when the Access Token expires.

</Admonition>
