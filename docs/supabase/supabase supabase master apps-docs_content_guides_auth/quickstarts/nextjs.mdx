---
title: 'Use Supabase Auth with Next.js'
subtitle: 'Learn how to configure Supabase Auth for the Next.js App Router.'
breadcrumb: 'Auth Quickstarts'
hideToc: true
---

<StepHikeCompact>

  <StepHikeCompact.Step step={1}>
    <StepHikeCompact.Details title="Create a new Supabase project">

    Head over to [database.new](https://database.new) and create a new Supabase project.

    Your new database has a table for storing your users. You can see that this table is currently empty by running some SQL in the [SQL Editor](https://supabase.com/dashboard/project/_/sql/new).

    </StepHikeCompact.Details>

    <StepHikeCompact.Code>

     ```sql name=SQL_EDITOR
      select * from auth.users;
      ````

    </StepHikeCompact.Code>

  </StepHikeCompact.Step>

  <StepHikeCompact.Step step={2}>

    <StepHikeCompact.Details title="Create a Next.js app">

    Use the `create-next-app` command and the `with-supabase` template, to create a Next.js app pre-configured with:
    - [Cookie-based Auth](https://supabase.com/docs/guides/auth/auth-helpers/nextjs)
    - [TypeScript](https://www.typescriptlang.org/)
    - [Tailwind CSS](https://tailwindcss.com/)

    [See GitHub repo](https://github.com/vercel/next.js/tree/canary/examples/with-supabase)

    </StepHikeCompact.Details>

    <StepHikeCompact.Code>

      ```bash name=Terminal
      npx create-next-app -e with-supabase
      ```

    </StepHikeCompact.Code>

  </StepHikeCompact.Step>

  <StepHikeCompact.Step step={3}>
    <StepHikeCompact.Details title="Declare Supabase Environment Variables">

    Rename `.env.example` to `.env.local` and populate with [your project's URL and Anon Key](https://supabase.com/dashboard/project/_/settings/api).

    </StepHikeCompact.Details>

    <StepHikeCompact.Code>

      ```text name=.env.local
        NEXT_PUBLIC_SUPABASE_URL=your-project-url
        NEXT_PUBLIC_SUPABASE_ANON_KEY=your-anon-key
      ```

    </StepHikeCompact.Code>

  </StepHikeCompact.Step>

  <StepHikeCompact.Step step={4}>
    <StepHikeCompact.Details title="Start the app">

    Start the development server, go to http://localhost:3000 in a browser, and you should see the contents of `app/page.tsx`.

    To sign up a new user, navigate to http://localhost:3000/sign-up, and click `Sign up`. *NOTE: .env.example must be renamed to .env.local before this route becomes available*

    </StepHikeCompact.Details>

    <StepHikeCompact.Code>

      ```bash name=Terminal
      npm run dev
      ```

    </StepHikeCompact.Code>

  </StepHikeCompact.Step>
</StepHikeCompact>

## Learn more

- [Setting up Server-Side Auth for Next.js](https://supabase.com/docs/guides/auth/server-side/nextjs) for a Next.js deep dive
- [Supabase Auth docs](https://supabase.com/docs/guides/auth#authentication) for more Supabase authentication methods
