---
id: 'auth-kakao'
title: 'Login with Kakao'
description: 'Add Kakao OAuth to your Supabase project'
---

To enable Ka<PERSON>o Auth for your project, you need to set up an Kakao OAuth application and add the application credentials to your Supabase Dashboard.

## Overview

Kakao OAuth consists of six broad steps:

- Create and configure your app in the [Kakao Developer Portal](https://developers.kakao.com).
- Obtaining a `REST API key` - this will serve as the `client_id`.
- Generating the `Client secret code` - this will serve as the `client_secret`.
- Additional configurations on Kakao Developers Portal.
- Add your `client id` and `client secret` keys to your [Supabase Project](https://supabase.com/dashboard).
- Add the login code to your [Supabase JS Client App](https://github.com/supabase/supabase-js).

## Access your Kakao Developer account

- Go to [Kakao Developers Portal](https://developers.kakao.com).
- Click on `Login` at the top right to log in.

![Kakao Developers Portal.](/docs/img/guides/auth-kakao/kakao-developers-page.png)

## Create and configure your app

- Go to `My Application`.
- Click on `Add an application` at the top.
- Fill out your app information:
  - App icon.
  - App name.
  - Company name.
- Click `Save` at the bottom right.

## Obtain a REST API key

This will serve as the `client_id` when you make API calls to authenticate the user.

- Go to `My Application`.
- Click on your app.
- You will be redirected to `Summary` tab of your app.
- In the `App Keys` section you will see `REST API key` -- this ID will become your `client_id` later.

## Find your callback URL

<$Partial path="social_provider_setup.mdx" variables={{ "provider": "Kakao" }} />

- To add callback URL on Kakao, go to `Product settings` >
  `Kakao Login` > `Redirect URI`.

## Generate and activate a `client_secret`

- Go to `Product settings` > `Kakao Login` > `Security`.
- Click on the `Kakao Login` switch to enable Kakao Login.
- Click on `generate code` at the bottom to generate the `Client secret code` -- this will serve as a `client_secret` for your Supabase project.
- Make sure you enabled `Client secret code` by selecting `enable` from the `Activation state` section.

## Additional configurations on Kakao Developers portal

- Make sure the Kakao Login is enabled in the `Kakao Login` tab.
- Set following scopes under the "Consent Items": account_email, profile_image, profile_nickname

![Consent items needs to be set.](/docs/img/guides/auth-kakao/kakao-developers-consent-items-set.png)

## Add your OAuth credentials to Supabase

<$Partial path="social_provider_settings_supabase.mdx" variables={{ "provider": "Kakao" }} />

## Add login code to your client app

<Tabs
  scrollable
  size="small"
  type="underlined"
  defaultActiveId="js"
  queryGroup="language"
>
<TabPanel id="js" label="JavaScript">

<$Partial path="create_client_snippet.mdx" />

When your user signs in, call [`signInWithOAuth()`](/docs/reference/javascript/auth-signinwithoauth) with `kakao` as the `provider`:

```js
import { createClient } from '@supabase/supabase-js'
const supabase = createClient('<your-project-url>', '<your-anon-key>')

// ---cut---
async function signInWithKakao() {
  const { data, error } = await supabase.auth.signInWithOAuth({
    provider: 'kakao',
  })
}
```

</TabPanel>
<TabPanel id="flutter" label="Flutter">

When your user signs in, call [`signInWithOAuth()`](/docs/reference/dart/auth-signinwithoauth) with `kakao` as the `provider`:

```dart
Future<void> signInWithKakao() async {
  await supabase.auth.signInWithOAuth(
    OAuthProvider.kakao,
    redirectTo: kIsWeb ? null : 'my.scheme://my-host', // Optionally set the redirect link to bring back the user via deeplink.
    authScreenLaunchMode:
        kIsWeb ? LaunchMode.platformDefault : LaunchMode.externalApplication, // Launch the auth screen in a new webview on mobile.
  );
}
```

</TabPanel>
<TabPanel id="kotlin" label="Kotlin">

When your user signs in, call [signInWith(Provider)](/docs/reference/kotlin/auth-signinwithoauth) with `Kakao` as the `Provider`:

```kotlin
suspend fun signInWithKakao() {
	supabase.auth.signInWith(Kakao)
}
```

</TabPanel>
</Tabs>

<$Partial path="oauth_pkce_flow.mdx" />

<Tabs
  scrollable
  size="small"
  type="underlined"
  defaultActiveId="js"
  queryGroup="language"
>
<TabPanel id="js" label="JavaScript">

When your user signs out, call [signOut()](/docs/reference/javascript/auth-signout) to remove them from the browser session and any objects from localStorage:

```js
async function signOut() {
  const { error } = await supabase.auth.signOut()
}
```

</TabPanel>
<TabPanel id="flutter" label="Flutter">

When your user signs out, call [signOut()](/docs/reference/dart/auth-signout) to remove them from the browser session and any objects from localStorage:

```dart
Future<void> signOut() async {
  await supabase.auth.signOut();
}
```

</TabPanel>
<TabPanel id="kotlin" label="Kotlin">

When your user signs out, call [signOut()](/docs/reference/kotlin/auth-signout) to remove them from the browser session and any objects from localStorage:

```kotlin
suspend fun signOut() {
	supabase.auth.signOut()
}
```

</TabPanel>
</Tabs>

## Using Kakao Login JS SDK

[Kakao Login JS SDK](https://developers.kakao.com/docs/latest/en/kakaologin/js) is an official Kakao SDK for authenticating Kakao users on websites.

Exchange the [authorization code returned by Kakao API](https://developers.kakao.com/docs/latest/en/kakaologin/rest-api#request-code) for an [ID Token](https://developers.kakao.com/docs/latest/en/kakaologin/common#login-with-oidc).

For example, this code shows a how to get ID Token:

```
const requestUrl = new URL(request.url);
const code = requestUrl.searchParams.get('code');

if (code) {
  const res = await fetch('https://kauth.kakao.com/oauth/token', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/x-www-form-urlencoded;charset=utf-8',
    },
    body: new URLSearchParams({
      grant_type: 'authorization_code',
      client_id: '<CLIENT_ID>',
      redirect_uri: '<url>/api/auth/kakao/oidc',
      code,
      client_secret: '<CLIENT_SECRET>',
    }),
  });

  const {id_token} = await res.json();
}
```

Use the ID Token to sign in:

```
const res = await auth.signInWithIdToken({
  provider: 'kakao',
  token: id_token,
});
```

### Configuration

1. Set 'State' to 'ON' under [OpenID Connect Activation](https://developers.kakao.com/docs/latest/en/kakaologin/prerequisite#activate-oidc) on Kakao Developers portal Application Dashboard.
2. Add `openid` to [scope](https://developers.kakao.com/docs/latest/en/kakaologin/common#additional-consent-scope) along with the scope values you wish to obtain consent for.

## Resources

- [Kakao Developers Portal](https://developers.kakao.com).
