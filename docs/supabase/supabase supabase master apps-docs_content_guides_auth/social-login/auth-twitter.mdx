---
id: 'auth-twitter'
title: 'Login with Twitter'
description: 'Add Twitter OAuth to your Supabase project'
---

To enable Twitter Auth for your project, you need to set up a Twitter OAuth application and add the application credentials in the Supabase Dashboard.

## Overview

Setting up Twitter logins for your application consists of 3 parts:

- Create and configure a Twitter Project and App on the [Twitter Developer Dashboard](https://developer.twitter.com/en/portal/dashboard).
- Add your Twitter `API Key` and `API Secret Key` to your [Supabase Project](https://supabase.com/dashboard).
- Add the login code to your [Supabase JS Client App](https://github.com/supabase/supabase-js).

## Access your Twitter Developer account

- Go to [developer.twitter.com](https://developer.twitter.com).
- Click on `Sign in` at the top right to log in.

![Twitter Developer Portal.](/docs/img/guides/auth-twitter/twitter-portal.png)

## Find your callback URL

<$Partial path="social_provider_setup.mdx" variables={{ "provider": "Twitter" }} />

## Create a Twitter OAuth app

- Click `+ Create Project`.
  - Enter your project name, click `Next`.
  - Select your use case, click `Next`.
  - Enter a description for your project, click `Next`.
  - Enter a name for your app, click `Next`.
  - Copy and save your `API Key` (this is your `client_id`).
  - Copy and save your `API Secret Key` (this is your `client_secret`).
  - Click on `App settings` to proceed to next steps.
- At the bottom, you will find `User authentication settings`. Click on `Set up`.
- Under `User authentication settings`, you can configure `App permissions`.
- Make sure you turn ON `Request email from users`.
- Select `Web App...` as the `Type of App`.
- Under `App info` configure the following.
  - Enter your `Callback URL`. Check the **Find your callback URL** section above to learn how to obtain your callback URL.
  - Enter your `Website URL` (tip: try `http://127.0.0.1:port` or `http://www.localhost:port` during development)
  - Enter your `Terms of service URL`.
  - Enter your `Privacy policy URL`.
- Click `Save`.

## Enter your Twitter credentials into your Supabase project

<$Partial path="social_provider_settings_supabase.mdx" variables={{ "provider": "Twitter" }} />

You can also configure the Twitter auth provider using the Management API:

```bash
# Get your access token from https://supabase.com/dashboard/account/tokens
export SUPABASE_ACCESS_TOKEN="your-access-token"
export PROJECT_REF="your-project-ref"

# Configure Twitter auth provider
curl -X PATCH "https://api.supabase.com/v1/projects/$PROJECT_REF/config/auth" \
  -H "Authorization: Bearer $SUPABASE_ACCESS_TOKEN" \
  -H "Content-Type: application/json" \
  -d '{
    "external_twitter_enabled": true,
    "external_twitter_client_id": "your-twitter-api-key",
    "external_twitter_secret": "your-twitter-api-secret-key"
  }'
```

## Add login code to your client app

<Tabs
  scrollable
  size="small"
  type="underlined"
  defaultActiveId="js"
  queryGroup="language"
>
<TabPanel id="js" label="JavaScript">

<$Partial path="create_client_snippet.mdx" />

When your user signs in, call [`signInWithOAuth()`](/docs/reference/javascript/auth-signinwithoauth) with `twitter` as the `provider`:

```js
import { createClient } from '@supabase/supabase-js'
const supabase = createClient('https://your-project-id.supabase.co', 'your-anon-key')

// ---cut---
async function signInWithTwitter() {
  const { data, error } = await supabase.auth.signInWithOAuth({
    provider: 'twitter',
  })
}
```

</TabPanel>
<TabPanel id="flutter" label="Flutter">

When your user signs in, call [`signInWithOAuth()`](/docs/reference/dart/auth-signinwithoauth) with `twitter` as the `provider`:

```dart
Future<void> signInWithTwitter() async {
  await supabase.auth.signInWithOAuth(
    OAuthProvider.twitter,
    redirectTo: kIsWeb ? null : 'my.scheme://my-host', // Optionally set the redirect link to bring back the user via deeplink.
    authScreenLaunchMode:
        kIsWeb ? LaunchMode.platformDefault : LaunchMode.externalApplication, // Launch the auth screen in a new webview on mobile.
  );
}
```

</TabPanel>
<TabPanel id="kotlin" label="Kotlin">

When your user signs in, call [signInWith(Provider)](/docs/reference/kotlin/auth-signinwithoauth) with `Twitter` as the `Provider`:

```kotlin
suspend fun signInWithTwitter() {
	supabase.auth.signInWith(Twitter)
}
```

</TabPanel>
</Tabs>

<$Partial path="oauth_pkce_flow.mdx" />

<Tabs
  scrollable
  size="small"
  type="underlined"
  defaultActiveId="js"
  queryGroup="language"
>
<TabPanel id="js" label="JavaScript">

When your user signs out, call [signOut()](/docs/reference/javascript/auth-signout) to remove them from the browser session and any objects from localStorage:

```js
import { createClient } from '@supabase/supabase-js'
const supabase = createClient('https://your-project-id.supabase.co', 'your-anon-key')

// ---cut---
async function signOut() {
  const { error } = await supabase.auth.signOut()
}
```

</TabPanel>
<TabPanel id="flutter" label="Flutter">

When your user signs out, call [signOut()](/docs/reference/dart/auth-signout) to remove them from the browser session and any objects from localStorage:

```dart
Future<void> signOut() async {
  await supabase.auth.signOut();
}
```

</TabPanel>
<TabPanel id="kotlin" label="Kotlin">

When your user signs out, call [signOut()](/docs/reference/kotlin/auth-signout) to remove them from the browser session and any objects from localStorage:

```kotlin
suspend fun signOut() {
	supabase.auth.signOut()
}
```

</TabPanel>
</Tabs>

## Resources

- [Supabase - Get started for free](https://supabase.com)
- [Supabase JS Client](https://github.com/supabase/supabase-js)
- [Twitter Developer Dashboard](https://developer.twitter.com/en/portal/dashboard)
