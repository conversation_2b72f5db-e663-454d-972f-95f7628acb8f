---
id: 'auth-apple'
title: 'Login with Apple'
description: 'Use Sign in with Apple with Supabase'
tocVideo: '-tpcZzTdvN0'
---

<PERSON><PERSON><PERSON> Auth supports using [Sign in with Apple](https://developer.apple.com/sign-in-with-apple/) on the web and in native apps for iOS, macOS, watchOS or tvOS.

## Overview

To support Sign in with Apple, you need to configure the [Apple provider in the Supabase dashboard](https://supabase.com/dashboard/project/_/auth/providers) for your project.

There are three general ways to use Sign in with Apple, depending on the application you're trying to build:

- Sign in on the web or in web-based apps
  - Using an OAuth flow initiated by <PERSON><PERSON><PERSON> Auth using the [Sign in with Apple REST API](https://developer.apple.com/documentation/signinwithapplerestapi).
  - Using [Sign in with Apple JS](https://developer.apple.com/documentation/sign_in_with_apple/sign_in_with_apple_js) directly in the browser, usually suitable for websites.
- Sign in natively inside iOS, macOS, watchOS or tvOS apps using [Apple's Authentication Services](https://developer.apple.com/documentation/authenticationservices)

In some cases you're able to use the OAuth flow within web-based native apps such as with [React Native](https://reactnative.dev), [Expo](https://expo.dev) or other similar frameworks. It is best practice to use native Sign in with Apple capabilities on those platforms instead.

When developing with Expo, you can test Sign in with Apple via the Expo Go app, in all other cases you will need to obtain an [Apple Developer](https://developer.apple.com) account to enable the capability.

<Tabs
  scrollable
  size="large"
  type="underlined"
  defaultActiveId="web"
  queryGroup="platform"
>
  <TabPanel id="web" label="Web">

    ## Using the OAuth flow for web

    Sign in with Apple's OAuth flow is designed for web or browser based sign in methods. It can be used on web-based apps as well as websites, though some users can benefit by using Sign in with Apple JS directly.

    Behind the scenes, Supabase Auth uses the [REST APIs](https://developer.apple.com/documentation/signinwithapplerestapi) provided by Apple.

    <$Partial path="create_client_snippet.mdx" />

    To initiate sign in, you can use the `signInWithOAuth()` method from the Supabase JavaScript library:

    ```ts
    import { createClient } from '@supabase/supabase-js'
    const supabase = createClient('https://your-project.supabase.co', 'your-anon-key')

    // ---cut---
    supabase.auth.signInWithOAuth({
      provider: 'apple',
    })
    ```

    This call takes the user to Apple's consent screen. Once the flow ends, the user's profile information is exchanged and validated with Supabase Auth before it redirects back to your web application with an access and refresh token representing the user's session.

    <$Partial path="oauth_pkce_flow.mdx" />

    ### Configuration [#configuration-web]

    You will require the following information:

    1. Your Apple Developer account's **Team ID**, which is an alphanumeric string of 10 characters that uniquely identifies the developer of the app. It's often accessible in the upper right-side menu on the Apple Developer Console.
    2. Register email sources for _Sign in with Apple for Email Communication_ which can be found in the [Services](https://developer.apple.com/account/resources/services/list) section of the Apple Developer Console.
    3. An **App ID** which uniquely identifies the app you are building. You can create a new App ID from the [Identifiers](https://developer.apple.com/account/resources/identifiers/list/bundleId) section in the Apple Developer Console (use the filter menu in the upper right side to see all App IDs). These usually are a reverse domain name string, for example `com.example.app`. Make sure you configure Sign in with Apple once you create an App ID in the Capabilities list. At this time Supabase Auth does not support Server-to-Server notification endpoints, so you should leave that setting blank. (In the past App IDs were referred to as _bundle IDs._)
    4. A **Services ID** which uniquely identifies the web services provided by the app you registered in the previous step. You can create a new Services ID from the [Identifiers](https://developer.apple.com/account/resources/identifiers/list/serviceId) section in the Apple Developer Console (use the filter menu in the upper right side to see all Services IDs). These usually are a reverse domain name string, for example `com.example.app.web`.
    5. Configure Website URLs for the newly created **Services ID**. The web domain you should use is the domain your Supabase project is hosted on. This is usually `<project-id>.supabase.co` while the redirect URL is `https://<project-id>.supabase.co/auth/v1/callback`.
    6. Create a signing **Key** in the [Keys](https://developer.apple.com/account/resources/authkeys/list) section of the Apple Developer Console. You can use this key to generate a secret key using the tool below, which is added to your Supabase project's Auth configuration. Make sure you safely store the `AuthKey_XXXXXXXXXX.p8` file. If you ever lose access to it, or make it public accidentally, revoke it from the Apple Developer Console and create a new one immediately. You will have to generate a new secret key using this file every 6 months, so make sure you schedule a recurring meeting in your calendar!
    7. Finally, add the information you configured above to the [Apple provider configuration in the Supabase dashboard](https://supabase.com/dashboard/project/_/auth/providers).

You can also configure the Apple auth provider using the Management API:

```bash
# Get your access token from https://supabase.com/dashboard/account/tokens
export SUPABASE_ACCESS_TOKEN="your-access-token"
export PROJECT_REF="your-project-ref"

# Configure Apple auth provider
curl -X PATCH "https://api.supabase.com/v1/projects/$PROJECT_REF/config/auth" \
  -H "Authorization: Bearer $SUPABASE_ACCESS_TOKEN" \
  -H "Content-Type: application/json" \
  -d '{
    "external_apple_enabled": true,
    "external_apple_client_id": "your-services-id",
    "external_apple_secret": "your-generated-secret-key"
  }'
```

    <Admonition type="tip">

    Use this tool to generate a new Apple client secret. No keys leave your browser! Be aware that this tool does not currently work in Safari, so use Firefox or a Chrome-based browser instead.

    </Admonition>

    <AppleSecretGenerator />

    ## Using sign in with Apple JS

    [Sign in with Apple JS](https://developer.apple.com/documentation/sign_in_with_apple/sign_in_with_apple_js) is an official Apple framework for authenticating Apple users on websites. Although it can be used in web-based apps, those use cases will benefit more with the OAuth flow described above. We recommend using this method on classic websites only.

    You can use the `signInWithIdToken()` method from the Supabase JavaScript library on the website to obtain an access and refresh token once the user has given consent using Sign in with Apple JS:

    ```ts
    function signIn() {
      const data = await AppleID.auth.signIn()

      await supabase.auth.signInWithIdToken({
        provider: 'apple',
        token: data.id_token,
        nonce: '<nonce used in AppleID.auth.init>',
      })
    }
    ```

    Alternatively, you can use the `AppleIDSignInOnSuccess` event with the `usePopup` option:

    ```ts
    // Listen for authorization success.
    document.addEventListener('AppleIDSignInOnSuccess', async (event) => {
      await supabase.auth.signInWithIdToken({
        provider: 'apple',
        token: event.data.id_token,
        nonce: '<value used in appleid-signin-nonce meta tag>',
      })
    })
    ```

    Make sure you request for the scope `name email` when initializing the library.

    ### Configuration [#configuration-apple-js]

    To use Sign in with Apple JS you need to configure these options:

    1. Have an **App ID** which uniquely identifies the app you are building. You can create a new App ID from the [Identifiers](https://developer.apple.com/account/resources/identifiers/list/bundleId) section in the Apple Developer Console (use the filter menu in the upper right side to see all App IDs). These usually are a reverse domain name string, for example `com.example.app`. Make sure you configure Sign in with Apple for the App ID you created or already have, in the Capabilities list. At this time Supabase Auth does not support Server-to-Server notification endpoints, so you should leave that setting blank. (In the past App IDs were referred to as _bundle IDs._)
    2. Obtain a **Services ID** attached to the App ID that uniquely identifies the website. Use this value as the client ID when initializing Sign in with Apple JS. You can create a new Services ID from the [Identifiers](https://developer.apple.com/account/resources/identifiers/list/serviceId) section in the Apple Developer Console (use the filter menu in the upper right side to see all Services IDs). These usually are a reverse domain name string, for example `com.example.app.website`.
    3. Configure Website URLs for the newly created **Services ID**. The web domain you should use is the domain your website is hosted on. The redirect URL must also point to a page on your website that will receive the callback from Apple.
    4. Register the Services ID you created to your project's [Apple provider configuration in the Supabase dashboard](https://supabase.com/dashboard/project/_/auth/providers) under _Client IDs_.

    <Admonition type="note">

      If you're using Sign in with Apple JS you do not need to configure the OAuth settings.

    </Admonition>

  </TabPanel>

  <TabPanel id="react-native" label="Expo React Native">

    ## Using native sign in with Apple in Expo

    When working with Expo, you can use the [Expo AppleAuthentication](https://docs.expo.dev/versions/latest/sdk/apple-authentication/) library to obtain an ID token that you can pass to supabase-js [`signInWithIdToken` method](/docs/reference/javascript/auth-signinwithidtoken).

    Follow the [Expo docs](https://docs.expo.dev/versions/latest/sdk/apple-authentication/#installation) for installation and configuration instructions. See the [supabase-js reference](/docs/reference/javascript/initializing?example=react-native-options-async-storage) for instructions on initializing the supabase-js client in React Native.

    ```tsx name=./components/Auth.native.tsx
    import { Platform } from 'react-native'
    import * as AppleAuthentication from 'expo-apple-authentication'
    import { supabase } from 'app/utils/supabase'

    export function Auth() {
      if (Platform.OS === 'ios')
        return (
          <AppleAuthentication.AppleAuthenticationButton
            buttonType={AppleAuthentication.AppleAuthenticationButtonType.SIGN_IN}
            buttonStyle={AppleAuthentication.AppleAuthenticationButtonStyle.BLACK}
            cornerRadius={5}
            style={{ width: 200, height: 64 }}
            onPress={async () => {
              try {
                const credential = await AppleAuthentication.signInAsync({
                  requestedScopes: [
                    AppleAuthentication.AppleAuthenticationScope.FULL_NAME,
                    AppleAuthentication.AppleAuthenticationScope.EMAIL,
                  ],
                })
                // Sign in via Supabase Auth.
                if (credential.identityToken) {
                  const {
                    error,
                    data: { user },
                  } = await supabase.auth.signInWithIdToken({
                    provider: 'apple',
                    token: credential.identityToken,
                  })
                  console.log(JSON.stringify({ error, user }, null, 2))
                  if (!error) {
                    // User is signed in.
                  }
                } else {
                  throw new Error('No identityToken.')
                }
              } catch (e) {
                if (e.code === 'ERR_REQUEST_CANCELED') {
                  // handle that the user canceled the sign-in flow
                } else {
                  // handle other errors
                }
              }
            }}
          />
        )
      return <>{/* Implement Android Auth options. */}</>
    }
    ```

    When working with bare React Native, you can use [invertase/react-native-apple-authentication](https://github.com/invertase/react-native-apple-authentication) to obtain the ID token.

    ### Configuration [#expo-configuration-native-app]

    <Admonition type="note">

    When testing with Expo Go, the Expo App ID `host.exp.Exponent` will be used. Make sure to add this to the "Client IDs" list in your [Supabase dashboard Apple provider configuration](https://supabase.com/dashboard/project/_/auth/providers)!

    </Admonition>

    <Admonition type="note">

    When testing with Expo development build with custom `bundleIdentifier`, for example com.example.app , com.example.app.dev , com.example.app.preview. Make sure to add all these variants to the "Client IDs" list in your [Supabase dashboard Apple provider configuration](https://supabase.com/dashboard/project/_/auth/providers)!

    </Admonition>

    1. Have an **App ID** which uniquely identifies the app you are building. You can create a new App ID from the [Identifiers](https://developer.apple.com/account/resources/identifiers/list/bundleId) section in the Apple Developer Console (use the filter menu in the upper right side to see all App IDs). These usually are a reverse domain name string, for example `com.example.app`. Make sure you configure Sign in with Apple for the App ID you created or already have, in the Capabilities list. At this time Supabase Auth does not support Server-to-Server notification endpoints, so you should leave that setting blank. (In the past App IDs were referred to as _bundle IDs._)
    2. Register all of the App IDs that will be using your Supabase project in the [Apple provider configuration in the Supabase dashboard](https://supabase.com/dashboard/project/_/auth/providers) under _Client IDs_.

    <Admonition type="note">

    If you're building a native app only, you do not need to configure the OAuth settings.

    </Admonition>

  </TabPanel>

  <TabPanel id="flutter" label="Flutter">

    ## Apple sign in on iOS and macOS

    You can perform Apple sign in using the [sign_in_with_apple](https://pub.dev/packages/sign_in_with_apple) package on Flutter apps running on iOS or macOS.
    Follow the instructions in the package README to set up native Apple sign in on iOS and macOS.

    Once the setup is complete on the Flutter app, add the bundle ID of your app to your Supabase dashboard in `Authentication -> Providers -> Apple` in order to register your app with Supabase.

    ```dart
    import 'package:sign_in_with_apple/sign_in_with_apple.dart';
    import 'package:supabase_flutter/supabase_flutter.dart';
    import 'package:crypto/crypto.dart';

    /// Performs Apple sign in on iOS or macOS
    Future<AuthResponse> signInWithApple() async {
      final rawNonce = supabase.auth.generateRawNonce();
      final hashedNonce = sha256.convert(utf8.encode(rawNonce)).toString();

      final credential = await SignInWithApple.getAppleIDCredential(
        scopes: [
          AppleIDAuthorizationScopes.email,
          AppleIDAuthorizationScopes.fullName,
        ],
        nonce: hashedNonce,
      );

      final idToken = credential.identityToken;
      if (idToken == null) {
        throw const AuthException(
            'Could not find ID Token from generated credential.');
      }

      return supabase.auth.signInWithIdToken(
        provider: OAuthProvider.apple,
        idToken: idToken,
        nonce: rawNonce,
      );
    }
    ```

    ### Configuration [#flutter-configuration-native-app]

    1. Have an **App ID** which uniquely identifies the app you are building. You can create a new App ID from the [Identifiers](https://developer.apple.com/account/resources/identifiers/list/bundleId) section in the Apple Developer Console (use the filter menu in the upper right side to see all App IDs). These usually are a reverse domain name string, for example `com.example.app`. Make sure you configure Sign in with Apple for the App ID you created or already have, in the Capabilities list. At this time Supabase Auth does not support Server-to-Server notification endpoints, so you should leave that setting blank. (In the past App IDs were referred to as _bundle IDs._)
    2. Register all of the App IDs that will be using your Supabase project in the [Apple provider configuration in the Supabase dashboard](https://supabase.com/dashboard/project/_/auth/providers) under _Client IDs_.

    ## Apple sign in on Android, Web, Windows and Linux

    For platforms that doesn't support native Apple sign in, you can use the `signInWithOAuth()` method to perform the Apple sign in.

    <Admonition type="note">

    Do **NOT** follow the Android or Web setup instructions on [sign_in_with_apple](https://pub.dev/packages/sign_in_with_apple) package README for these platforms.　sign_in_with_apple package is not used for performing Apple sign-in on non-Apple platforms for Supabase.

    </Admonition>

    This method of signing in is web based, and will open a browser window to perform the sign in. For non-web platforms, the user is brought back to the app via [deep linking](/docs/guides/auth/native-mobile-deep-linking?platform=flutter).

    ```dart
    await supabase.auth.signInWithOAuth(
      OAuthProvider.apple,
      redirectTo: kIsWeb ? null : 'my.scheme://my-host', // Optionally set the redirect link to bring back the user via deeplink.
      authScreenLaunchMode:
          kIsWeb ? LaunchMode.platformDefault : LaunchMode.externalApplication, // Launch the auth screen in a new webview on mobile.
    );
    ```

    This call takes the user to Apple's consent screen. Once the flow ends, the user's profile information is exchanged and validated with Supabase Auth before it redirects back to your Flutter application with an access and refresh token representing the user's session.

    ### Configuration [#flutter-configuration-web]

    You will require the following information:

    1. Your Apple Developer account's **Team ID**, which is an alphanumeric string of 10 characters that uniquely identifies the developer of the app. It's often accessible in the upper right-side menu on the Apple Developer Console.
    2. Register email sources for _Sign in with Apple for Email Communication_ which can be found in the [Services](https://developer.apple.com/account/resources/services/list) section of the Apple Developer Console.
    3. An **App ID** which uniquely identifies the app you are building. You can create a new App ID from the [Identifiers](https://developer.apple.com/account/resources/identifiers/list/bundleId) section in the Apple Developer Console (use the filter menu in the upper right side to see all App IDs). These usually are a reverse domain name string, for example `com.example.app`. Make sure you configure Sign in with Apple once you create an App ID in the Capabilities list. At this time Supabase Auth does not support Server-to-Server notification endpoints, so you should leave that setting blank. (In the past App IDs were referred to as _bundle IDs._)
    4. A **Services ID** which uniquely identifies the web services provided by the app you registered in the previous step. You can create a new Services ID from the [Identifiers](https://developer.apple.com/account/resources/identifiers/list/serviceId) section in the Apple Developer Console (use the filter menu in the upper right side to see all Services IDs). These usually are a reverse domain name string, for example `com.example.app.web`.
    5. Configure Website URLs for the newly created **Services ID**. The web domain you should use is the domain your Supabase project is hosted on. This is usually `<project-id>.supabase.co` while the redirect URL is `https://<project-id>.supabase.co/auth/v1/callback`.
    6. Create a signing **Key** in the [Keys](https://developer.apple.com/account/resources/authkeys/list) section of the Apple Developer Console. You can use this key to generate a secret key using the tool below, which is added to your Supabase project's Auth configuration. Make sure you safely store the `AuthKey_XXXXXXXXXX.p8` file. If you ever lose access to it, or make it public accidentally, revoke it from the Apple Developer Console and create a new one immediately. You will have to generate a new secret key using this file every 6 months, so make sure you schedule a recurring reminder in your calendar!
    7. Finally, add the information you configured above to the [Apple provider configuration in the Supabase dashboard](https://supabase.com/dashboard/project/_/auth/providers).

    <Admonition type="tip">

    Use this tool to generate a new Apple client secret. No keys leave your browser! Be aware that this tool does not currently work in Safari, so use Firefox or a Chrome-based browser instead.

    </Admonition>

    <AppleSecretGenerator />

  </TabPanel>

  <TabPanel id="swift" label="Swift">

    ## Using native sign in with Apple in Swift

    For apps written in Swift, follow the [Apple Developer docs](https://developer.apple.com/documentation/sign_in_with_apple/implementing_user_authentication_with_sign_in_with_apple) for obtaining the ID token and then pass it to the [Swift client's `signInWithIdToken`](https://github.com/supabase-community/gotrue-swift/blob/main/Examples/Shared/Sources/SignInWithAppleView.swift#L36) method.

    ```swift
    import SwiftUI
    import AuthenticationServices
    import Supabase

    struct SignInView: View {
        let client = SupabaseClient(supabaseURL: URL(string: "your url")!, supabaseKey: "your anon key")

        var body: some View {
          SignInWithAppleButton { request in
            request.requestedScopes = [.email, .fullName]
          } onCompletion: { result in
            Task {
              do {
                guard let credential = try result.get().credential as? ASAuthorizationAppleIDCredential
                else {
                  return
                }

                guard let idToken = credential.identityToken
                  .flatMap({ String(data: $0, encoding: .utf8) })
                else {
                  return
                }
                  try await client.auth.signInWithIdToken(
                  credentials: .init(
                    provider: .apple,
                    idToken: idToken
                  )
                )
              } catch {
                dump(error)
              }
            }
          }
          .fixedSize()
        }
    }
    ```

    ### Configuration [#swift-configuration-native-app]

    1. Have an **App ID** which uniquely identifies the app you are building. You can create a new App ID from the [Identifiers](https://developer.apple.com/account/resources/identifiers/list/bundleId) section in the Apple Developer Console (use the filter menu in the upper right side to see all App IDs). These usually are a reverse domain name string, for example `com.example.app`. Make sure you configure Sign in with Apple for the App ID you created or already have, in the Capabilities list. At this time Supabase Auth does not support Server-to-Server notification endpoints, so you should leave that setting blank. (In the past App IDs were referred to as _bundle IDs._)
    2. Register all of the App IDs that will be using your Supabase project in the [Apple provider configuration in the Supabase dashboard](https://supabase.com/dashboard/project/_/auth/providers) under _Client IDs_.

    <Admonition type="note">

    If you're building a native app only, you do not need to configure the OAuth settings.

    </Admonition>

  </TabPanel>

  <TabPanel id="kotlin" label="Kotlin">

    ## Using native sign in with Apple in Kotlin

    When using [Compose Multiplatform](https://github.com/JetBrains/compose-multiplatform/), you can use the [compose-auth](https://supabase.com/docs/reference/kotlin/installing) plugin. On iOS it uses Native Apple Login automatically and on other platforms it uses `gotrue.signInWith(Apple)`.

    **Initialize the Supabase Client**

    ```kotlin
    val supabaseClient = createSupabaseClient(
    	supabaseUrl = "SUPABASE_URL",
    	supabaseKey = "SUPABASE_KEY"
    ) {
    	install(GoTrue)
    	install(ComposeAuth) {
    		appleNativeLogin()
    	}
    }
    ```

    **Use the Compose Auth plugin in your Auth Screen**

    ```kotlin
    val authState = supabaseClient.composeAuth.rememberLoginWithApple(
    	onResult = {
    		when(it) { //handle errors
    			NativeSignInResult.ClosedByUser -> TODO()
    			is NativeSignInResult.Error -> TODO()
    			is NativeSignInResult.NetworkError -> TODO()
    			NativeSignInResult.Success -> TODO()
    		}
    	}
    )

    Button(onClick = { authState.startFlow() }) {
    	Text("Sign in with Apple")
    }
    ```

  </TabPanel>
</Tabs>
